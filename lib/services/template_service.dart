import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/qr_template.dart';

class TemplateService {
  static final TemplateService _instance = TemplateService._internal();
  factory TemplateService() => _instance;
  TemplateService._internal();

  List<QRTemplate> _templates = [];
  bool _isLoaded = false;

  /// Load templates from JSON file
  Future<void> loadTemplates() async {
    if (_isLoaded) return;

    try {
      final String jsonString = await rootBundle.loadString('assets/templates/templates.json');
      final List<dynamic> jsonList = json.decode(jsonString);
      
      _templates = jsonList.map((json) => QRTemplate.fromJson(json)).toList();
      _isLoaded = true;
    } catch (e) {
      print('Error loading templates: $e');
      // Load default templates if JSON fails
      _loadDefaultTemplates();
      _isLoaded = true;
    }
  }

  /// Get all templates
  Future<List<QRTemplate>> getAllTemplates() async {
    await loadTemplates();
    return _templates;
  }

  /// Get templates by category
  Future<List<QRTemplate>> getTemplatesByCategory(TemplateCategory category) async {
    await loadTemplates();
    if (category == TemplateCategory.all) {
      return _templates;
    }
    return _templates.where((template) => template.category == category).toList();
  }

  /// Search templates by keyword
  Future<List<QRTemplate>> searchTemplates(String keyword) async {
    await loadTemplates();
    final lowerKeyword = keyword.toLowerCase();
    return _templates.where((template) =>
        template.name.toLowerCase().contains(lowerKeyword) ||
        template.description.toLowerCase().contains(lowerKeyword)
    ).toList();
  }

  /// Get template by ID
  Future<QRTemplate?> getTemplateById(String id) async {
    await loadTemplates();
    try {
      return _templates.firstWhere((template) => template.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get featured/hot templates
  Future<List<QRTemplate>> getFeaturedTemplates() async {
    await loadTemplates();
    return _templates.where((template) => template.category == TemplateCategory.hot).toList();
  }

  /// Load default templates when JSON loading fails
  void _loadDefaultTemplates() {
    _templates = [
      // Hot Templates
      const QRTemplate(
        id: 'hot_1',
        name: 'Fire Gradient',
        description: 'Hot gradient background with fire theme',
        category: TemplateCategory.hot,
        backgroundImagePath: 'assets/images/templates/fire_gradient.png',
        qrPosition: QRPosition(x: 50, y: 100, width: 200, height: 200),
        thumbnailPath: 'assets/images/thumbnails/fire_gradient_thumb.png',
      ),
      const QRTemplate(
        id: 'hot_2',
        name: 'Neon Glow',
        description: 'Neon glow effect with dark background',
        category: TemplateCategory.hot,
        backgroundImagePath: 'assets/images/templates/neon_glow.png',
        qrPosition: QRPosition(x: 75, y: 125, width: 150, height: 150),
        thumbnailPath: 'assets/images/thumbnails/neon_glow_thumb.png',
      ),

      // Social Templates
      const QRTemplate(
        id: 'social_1',
        name: 'Social Media',
        description: 'Perfect for social media sharing',
        category: TemplateCategory.social,
        backgroundImagePath: 'assets/images/templates/social_media.png',
        qrPosition: QRPosition(x: 60, y: 80, width: 180, height: 180),
        thumbnailPath: 'assets/images/thumbnails/social_media_thumb.png',
      ),
      const QRTemplate(
        id: 'social_2',
        name: 'Instagram Style',
        description: 'Instagram-inspired design',
        category: TemplateCategory.social,
        backgroundImagePath: 'assets/images/templates/instagram_style.png',
        qrPosition: QRPosition(x: 70, y: 90, width: 160, height: 160),
        thumbnailPath: 'assets/images/thumbnails/instagram_style_thumb.png',
      ),

      // Business Templates
      const QRTemplate(
        id: 'business_1',
        name: 'Professional',
        description: 'Clean professional design for business',
        category: TemplateCategory.business,
        backgroundImagePath: 'assets/images/templates/professional.png',
        qrPosition: QRPosition(x: 50, y: 50, width: 200, height: 200),
        thumbnailPath: 'assets/images/thumbnails/professional_thumb.png',
      ),
      const QRTemplate(
        id: 'business_2',
        name: 'Corporate',
        description: 'Corporate style with logo space',
        category: TemplateCategory.business,
        backgroundImagePath: 'assets/images/templates/corporate.png',
        qrPosition: QRPosition(x: 75, y: 100, width: 150, height: 150),
        thumbnailPath: 'assets/images/thumbnails/corporate_thumb.png',
      ),

      // Love Templates
      const QRTemplate(
        id: 'love_1',
        name: 'Heart Theme',
        description: 'Romantic heart-themed design',
        category: TemplateCategory.love,
        backgroundImagePath: 'assets/images/templates/heart_theme.png',
        qrPosition: QRPosition(x: 65, y: 85, width: 170, height: 170),
        thumbnailPath: 'assets/images/thumbnails/heart_theme_thumb.png',
      ),
      const QRTemplate(
        id: 'love_2',
        name: 'Valentine',
        description: 'Perfect for Valentine\'s Day',
        category: TemplateCategory.love,
        backgroundImagePath: 'assets/images/templates/valentine.png',
        qrPosition: QRPosition(x: 80, y: 110, width: 140, height: 140),
        thumbnailPath: 'assets/images/thumbnails/valentine_thumb.png',
      ),

      // WiFi Templates
      const QRTemplate(
        id: 'wifi_1',
        name: 'WiFi Signal',
        description: 'WiFi signal icon design',
        category: TemplateCategory.wifi,
        backgroundImagePath: 'assets/images/templates/wifi_signal.png',
        qrPosition: QRPosition(x: 55, y: 75, width: 190, height: 190),
        thumbnailPath: 'assets/images/thumbnails/wifi_signal_thumb.png',
      ),
      const QRTemplate(
        id: 'wifi_2',
        name: 'Network',
        description: 'Network connectivity theme',
        category: TemplateCategory.wifi,
        backgroundImagePath: 'assets/images/templates/network.png',
        qrPosition: QRPosition(x: 70, y: 95, width: 160, height: 160),
        thumbnailPath: 'assets/images/thumbnails/network_thumb.png',
      ),
    ];
  }

  /// Refresh templates (reload from source)
  Future<void> refreshTemplates() async {
    _isLoaded = false;
    _templates.clear();
    await loadTemplates();
  }
}
