import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/qr_configuration.dart';
import '../models/qr_code_type.dart';

class FileService {
  static final FileService _instance = FileService._internal();
  factory FileService() => _instance;
  FileService._internal();

  /// Save QR code image to app documents directory
  Future<SaveResult> saveToGallery(Uint8List imageBytes, QRConfiguration config) async {
    try {
      // Save to app documents directory for now
      final appDir = await getApplicationDocumentsDirectory();
      final qrDir = Directory('${appDir.path}/qr_codes');

      if (!await qrDir.exists()) {
        await qrDir.create(recursive: true);
      }

      final fileName = config.fileName;
      final file = File('${qrDir.path}/$fileName');
      await file.writeAsBytes(imageBytes);

      return SaveResult(success: true, filePath: file.path);
    } catch (e) {
      return SaveResult(success: false, error: 'Error saving image: $e');
    }
  }

  /// Share QR code image
  Future<ShareResult> shareImage(Uint8List imageBytes, QRConfiguration config) async {
    // TODO: Implement sharing functionality
    return ShareResult(success: false, error: 'Sharing not yet implemented');
  }

  /// Share to specific platform
  Future<ShareResult> shareToWhatsApp(Uint8List imageBytes, QRConfiguration config) async {
    // TODO: Implement WhatsApp sharing
    return ShareResult(success: false, error: 'WhatsApp sharing not yet implemented');
  }

  /// Share to Telegram
  Future<ShareResult> shareToTelegram(Uint8List imageBytes, QRConfiguration config) async {
    // TODO: Implement Telegram sharing
    return ShareResult(success: false, error: 'Telegram sharing not yet implemented');
  }

  /// Share via email
  Future<ShareResult> shareViaEmail(Uint8List imageBytes, QRConfiguration config) async {
    // TODO: Implement email sharing
    return ShareResult(success: false, error: 'Email sharing not yet implemented');
  }

  /// Get saved QR codes from app directory
  Future<List<File>> getSavedQRCodes() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final qrDir = Directory('${appDir.path}/qr_codes');
      
      if (!await qrDir.exists()) {
        await qrDir.create(recursive: true);
        return [];
      }

      final files = await qrDir.list().where((entity) => 
        entity is File && entity.path.endsWith('.png')
      ).cast<File>().toList();

      return files;
    } catch (e) {
      print('Error getting saved QR codes: $e');
      return [];
    }
  }

  /// Save QR configuration for later use
  Future<bool> saveConfiguration(QRConfiguration config) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final configDir = Directory('${appDir.path}/configurations');
      
      if (!await configDir.exists()) {
        await configDir.create(recursive: true);
      }

      final configFile = File('${configDir.path}/${config.fileName.replaceAll('.png', '.json')}');
      await configFile.writeAsString(config.toJson().toString());
      
      return true;
    } catch (e) {
      print('Error saving configuration: $e');
      return false;
    }
  }

  /// Load saved configurations
  Future<List<QRConfiguration>> loadConfigurations() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final configDir = Directory('${appDir.path}/configurations');
      
      if (!await configDir.exists()) {
        return [];
      }

      final files = await configDir.list().where((entity) => 
        entity is File && entity.path.endsWith('.json')
      ).cast<File>().toList();

      final configurations = <QRConfiguration>[];
      for (final file in files) {
        try {
          final content = await file.readAsString();
          final config = QRConfiguration.fromJson(content as Map<String, dynamic>);
          configurations.add(config);
        } catch (e) {
          print('Error loading configuration from ${file.path}: $e');
        }
      }

      return configurations;
    } catch (e) {
      print('Error loading configurations: $e');
      return [];
    }
  }

  /// Request storage permission
  Future<bool> _requestStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      return status.isGranted;
    } else if (Platform.isIOS) {
      final status = await Permission.photos.request();
      return status.isGranted;
    }
    return true; // For other platforms, assume permission is granted
  }

  /// Clean up temporary files
  Future<void> cleanupTempFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final files = await tempDir.list().where((entity) => 
        entity is File && entity.path.contains('QR_code_')
      ).cast<File>().toList();

      for (final file in files) {
        try {
          await file.delete();
        } catch (e) {
          print('Error deleting temp file ${file.path}: $e');
        }
      }
    } catch (e) {
      print('Error cleaning up temp files: $e');
    }
  }
}

class SaveResult {
  final bool success;
  final String? error;
  final String? filePath;

  SaveResult({required this.success, this.error, this.filePath});
}

class ShareResult {
  final bool success;
  final String? error;

  ShareResult({required this.success, this.error});
}
