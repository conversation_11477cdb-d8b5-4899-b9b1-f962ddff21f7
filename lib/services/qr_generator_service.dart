import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:qr_flutter/qr_flutter.dart';
import '../models/qr_configuration.dart';
import '../models/qr_code_type.dart';
import '../models/qr_style.dart';

class QRGeneratorService {
  static final QRGeneratorService _instance = QRGeneratorService._internal();
  factory QRGeneratorService() => _instance;
  QRGeneratorService._internal();

  /// Validates QR code data based on type
  ValidationResult validateQRData(QRCodeType type, Map<String, dynamic> data) {
    switch (type) {
      case QRCodeType.text:
        return _validateText(data);
      case QRCodeType.url:
        return _validateUrl(data);
      case QRCodeType.email:
        return _validateEmail(data);
      case QRCodeType.phone:
        return _validatePhone(data);
      case QRCodeType.sms:
        return _validateSms(data);
      case QRCodeType.wifi:
        return _validateWifi(data);
      case QRCodeType.contact:
        return _validateContact(data);
      case QRCodeType.whatsapp:
        return _validateWhatsApp(data);
      case QRCodeType.telegram:
        return _validateTelegram(data);
    }
  }

  /// Generates QR code widget with styling
  Widget generateQRWidget(QRConfiguration config, {double size = 200}) {
    final qrData = QRCodeData(type: config.qrData.type, data: config.qrData.data);
    final qrString = qrData.generateQRString();

    if (qrString.isEmpty) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Text(
            'Enter data to generate QR code',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    return Container(
      width: size,
      height: size,
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: config.style.backgroundColor,
        borderRadius: BorderRadius.circular(config.style.cornerRadius),
      ),
      child: QrImageView(
        data: qrString,
        version: QrVersions.auto,
        size: size - 16, // Account for padding
        dataModuleStyle: QrDataModuleStyle(
          dataModuleShape: _getDataModuleShape(config.style.dataModuleShape),
          color: config.style.foregroundColor,
        ),
        eyeStyle: QrEyeStyle(
          eyeShape: _getEyeShape(config.style.eyeShape),
          color: config.style.eyeColor,
        ),
        embeddedImage: config.style.hasLogo && config.style.logoPath != null
            ? AssetImage(config.style.logoPath!)
            : null,
        embeddedImageStyle: QrEmbeddedImageStyle(
          size: Size(size * 0.2, size * 0.2),
        ),
      ),
    );
  }

  /// Generates QR code as image bytes
  Future<Uint8List?> generateQRImage(QRConfiguration config, {double size = 512}) async {
    try {
      final qrData = QRCodeData(type: config.qrData.type, data: config.qrData.data);
      final qrString = qrData.generateQRString();

      if (qrString.isEmpty) return null;

      // Create a widget to render
      final qrWidget = RepaintBoundary(
        key: GlobalKey(),
        child: Container(
          width: size,
          height: size,
          color: config.style.backgroundColor,
          child: generateQRWidget(config, size: size),
        ),
      );

      // For now, return a placeholder
      // TODO: Implement proper widget-to-image conversion
      return null;
    } catch (e) {
      debugPrint('Error generating QR image: $e');
      return null;
    }
  }

  // Helper methods for QR styling
  QrDataModuleShape _getDataModuleShape(QRDataModuleShape shape) {
    switch (shape) {
      case QRDataModuleShape.square:
        return QrDataModuleShape.square;
      case QRDataModuleShape.circle:
        return QrDataModuleShape.circle;
      default:
        return QrDataModuleShape.square;
    }
  }

  QrEyeShape _getEyeShape(QREyeShape shape) {
    switch (shape) {
      case QREyeShape.square:
        return QrEyeShape.square;
      case QREyeShape.circle:
        return QrEyeShape.circle;
      default:
        return QrEyeShape.square;
    }
  }

  // Validation methods
  ValidationResult _validateText(Map<String, dynamic> data) {
    final text = data['text'] as String?;
    if (text == null || text.isEmpty) {
      return ValidationResult(isValid: false, error: 'Text cannot be empty');
    }
    if (text.length > 2000) {
      return ValidationResult(isValid: false, error: 'Text is too long (max 2000 characters)');
    }
    return ValidationResult(isValid: true);
  }

  ValidationResult _validateUrl(Map<String, dynamic> data) {
    final url = data['url'] as String?;
    if (url == null || url.isEmpty) {
      return ValidationResult(isValid: false, error: 'URL cannot be empty');
    }
    
    final uri = Uri.tryParse(url);
    if (uri == null || !uri.hasScheme) {
      return ValidationResult(isValid: false, error: 'Invalid URL format');
    }
    
    return ValidationResult(isValid: true);
  }

  ValidationResult _validateEmail(Map<String, dynamic> data) {
    final email = data['email'] as String?;
    if (email == null || email.isEmpty) {
      return ValidationResult(isValid: false, error: 'Email cannot be empty');
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email)) {
      return ValidationResult(isValid: false, error: 'Invalid email format');
    }
    
    return ValidationResult(isValid: true);
  }

  ValidationResult _validatePhone(Map<String, dynamic> data) {
    final phone = data['phone'] as String?;
    if (phone == null || phone.isEmpty) {
      return ValidationResult(isValid: false, error: 'Phone number cannot be empty');
    }
    
    final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]+$');
    if (!phoneRegex.hasMatch(phone)) {
      return ValidationResult(isValid: false, error: 'Invalid phone number format');
    }
    
    return ValidationResult(isValid: true);
  }

  ValidationResult _validateSms(Map<String, dynamic> data) {
    final phoneValidation = _validatePhone(data);
    if (!phoneValidation.isValid) return phoneValidation;
    
    final message = data['message'] as String?;
    if (message != null && message.length > 160) {
      return ValidationResult(isValid: false, error: 'SMS message too long (max 160 characters)');
    }
    
    return ValidationResult(isValid: true);
  }

  ValidationResult _validateWifi(Map<String, dynamic> data) {
    final ssid = data['ssid'] as String?;
    if (ssid == null || ssid.isEmpty) {
      return ValidationResult(isValid: false, error: 'WiFi SSID cannot be empty');
    }
    
    final security = data['security'] as String?;
    if (security == null || !['WPA', 'WEP', 'nopass'].contains(security)) {
      return ValidationResult(isValid: false, error: 'Invalid security type');
    }
    
    if (security != 'nopass') {
      final password = data['password'] as String?;
      if (password == null || password.isEmpty) {
        return ValidationResult(isValid: false, error: 'Password required for secured network');
      }
    }
    
    return ValidationResult(isValid: true);
  }

  ValidationResult _validateContact(Map<String, dynamic> data) {
    final firstName = data['firstName'] as String?;
    final lastName = data['lastName'] as String?;
    
    if ((firstName == null || firstName.isEmpty) && (lastName == null || lastName.isEmpty)) {
      return ValidationResult(isValid: false, error: 'At least first name or last name is required');
    }
    
    return ValidationResult(isValid: true);
  }

  ValidationResult _validateWhatsApp(Map<String, dynamic> data) {
    return _validatePhone(data);
  }

  ValidationResult _validateTelegram(Map<String, dynamic> data) {
    final username = data['username'] as String?;
    if (username == null || username.isEmpty) {
      return ValidationResult(isValid: false, error: 'Telegram username cannot be empty');
    }
    
    final usernameRegex = RegExp(r'^[a-zA-Z0-9_]{5,32}$');
    if (!usernameRegex.hasMatch(username)) {
      return ValidationResult(isValid: false, error: 'Invalid Telegram username format');
    }
    
    return ValidationResult(isValid: true);
  }
}

class ValidationResult {
  final bool isValid;
  final String? error;

  ValidationResult({required this.isValid, this.error});
}
