import 'package:flutter/material.dart';
import '../models/qr_code_type.dart';

class QRInputForm extends StatefulWidget {
  final QRCodeType type;
  final Map<String, dynamic> initialData;
  final ValueChanged<Map<String, dynamic>> onDataChanged;
  final String? validationError;

  const QRInputForm({
    super.key,
    required this.type,
    required this.initialData,
    required this.onDataChanged,
    this.validationError,
  });

  @override
  State<QRInputForm> createState() => _QRInputFormState();
}

class _QRInputFormState extends State<QRInputForm> {
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, FocusNode> _focusNodes = {};

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void didUpdateWidget(QRInputForm oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.type != widget.type) {
      _disposeControllers();
      _initializeControllers();
    }
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  void _initializeControllers() {
    final fields = _getFieldsForType(widget.type);
    for (final field in fields) {
      _controllers[field.key] = TextEditingController(
        text: widget.initialData[field.key]?.toString() ?? '',
      );
      _focusNodes[field.key] = FocusNode();
      
      _controllers[field.key]!.addListener(() {
        _updateData();
      });
    }
  }

  void _disposeControllers() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    for (final focusNode in _focusNodes.values) {
      focusNode.dispose();
    }
    _controllers.clear();
    _focusNodes.clear();
  }

  void _updateData() {
    final data = <String, dynamic>{};
    for (final entry in _controllers.entries) {
      data[entry.key] = entry.value.text;
    }
    widget.onDataChanged(data);
  }

  List<FormField> _getFieldsForType(QRCodeType type) {
    switch (type) {
      case QRCodeType.text:
        return [
          FormField(key: 'text', label: 'Text', hint: 'Enter your text', maxLines: 3),
        ];
      case QRCodeType.url:
        return [
          FormField(key: 'url', label: 'URL', hint: 'https://example.com'),
        ];
      case QRCodeType.email:
        return [
          FormField(key: 'email', label: 'Email', hint: '<EMAIL>'),
          FormField(key: 'subject', label: 'Subject (Optional)', hint: 'Email subject'),
          FormField(key: 'body', label: 'Message (Optional)', hint: 'Email message', maxLines: 3),
        ];
      case QRCodeType.phone:
        return [
          FormField(key: 'phone', label: 'Phone Number', hint: '+1234567890'),
        ];
      case QRCodeType.sms:
        return [
          FormField(key: 'phone', label: 'Phone Number', hint: '+1234567890'),
          FormField(key: 'message', label: 'Message (Optional)', hint: 'SMS message', maxLines: 2),
        ];
      case QRCodeType.wifi:
        return [
          FormField(key: 'ssid', label: 'Network Name (SSID)', hint: 'MyWiFiNetwork'),
          FormField(key: 'password', label: 'Password', hint: 'WiFi password', obscureText: true),
          FormField(key: 'security', label: 'Security Type', hint: 'WPA/WEP/nopass'),
        ];
      case QRCodeType.contact:
        return [
          FormField(key: 'firstName', label: 'First Name', hint: 'John'),
          FormField(key: 'lastName', label: 'Last Name', hint: 'Doe'),
          FormField(key: 'phone', label: 'Phone (Optional)', hint: '+1234567890'),
          FormField(key: 'email', label: 'Email (Optional)', hint: '<EMAIL>'),
          FormField(key: 'organization', label: 'Organization (Optional)', hint: 'Company Name'),
          FormField(key: 'url', label: 'Website (Optional)', hint: 'https://example.com'),
        ];
      case QRCodeType.whatsapp:
        return [
          FormField(key: 'phone', label: 'Phone Number', hint: '+1234567890'),
          FormField(key: 'message', label: 'Message (Optional)', hint: 'WhatsApp message', maxLines: 3),
        ];
      case QRCodeType.telegram:
        return [
          FormField(key: 'username', label: 'Username', hint: 'telegram_username'),
        ];
    }
  }

  @override
  Widget build(BuildContext context) {
    final fields = _getFieldsForType(widget.type);
    
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...fields.map((field) => Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: TextFormField(
              controller: _controllers[field.key],
              focusNode: _focusNodes[field.key],
              decoration: InputDecoration(
                labelText: field.label,
                hintText: field.hint,
                border: const OutlineInputBorder(),
                errorText: widget.validationError,
              ),
              maxLines: field.maxLines,
              obscureText: field.obscureText,
              keyboardType: _getKeyboardType(field.key),
            ),
          )),
          
          if (widget.validationError != null)
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(top: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Theme.of(context).colorScheme.onErrorContainer,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.validationError!,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onErrorContainer,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  TextInputType _getKeyboardType(String key) {
    switch (key) {
      case 'email':
        return TextInputType.emailAddress;
      case 'phone':
        return TextInputType.phone;
      case 'url':
        return TextInputType.url;
      default:
        return TextInputType.text;
    }
  }
}

class FormField {
  final String key;
  final String label;
  final String hint;
  final int maxLines;
  final bool obscureText;

  const FormField({
    required this.key,
    required this.label,
    required this.hint,
    this.maxLines = 1,
    this.obscureText = false,
  });
}
