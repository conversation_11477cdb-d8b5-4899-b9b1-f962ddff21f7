import 'package:flutter/material.dart';
import '../models/qr_style.dart';

class EyeShapeSelector extends StatelessWidget {
  final QREyeShape selectedShape;
  final ValueChanged<QREyeShape> onShapeChanged;

  const EyeShapeSelector({
    super.key,
    required this.selectedShape,
    required this.onShapeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: QREyeShape.values.length,
      itemBuilder: (context, index) {
        final shape = QREyeShape.values[index];
        final isSelected = shape == selectedShape;
        
        return GestureDetector(
          onTap: () => onShapeChanged(shape),
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected 
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.outline,
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
              color: isSelected 
                  ? Theme.of(context).colorScheme.primaryContainer
                  : Theme.of(context).colorScheme.surface,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildEyeShapeIcon(shape, context),
                const SizedBox(height: 4),
                Text(
                  shape.displayName,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected 
                        ? Theme.of(context).colorScheme.onPrimaryContainer
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEyeShapeIcon(QREyeShape shape, BuildContext context) {
    final color = selectedShape == shape 
        ? Theme.of(context).colorScheme.onPrimaryContainer
        : Theme.of(context).colorScheme.onSurface;
    
    switch (shape) {
      case QREyeShape.square:
        return Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            border: Border.all(color: color, width: 2),
          ),
          child: Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              border: Border.all(color: color, width: 1),
            ),
          ),
        );
      
      case QREyeShape.circle:
        return Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: color, width: 2),
          ),
          child: Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: color, width: 1),
            ),
          ),
        );
      
      case QREyeShape.roundedSquare:
        return Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: color, width: 2),
          ),
          child: Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2),
              border: Border.all(color: color, width: 1),
            ),
          ),
        );
      
      case QREyeShape.diamond:
        return Transform.rotate(
          angle: 0.785398, // 45 degrees
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              border: Border.all(color: color, width: 2),
            ),
            child: Container(
              margin: const EdgeInsets.all(3),
              decoration: BoxDecoration(
                border: Border.all(color: color, width: 1),
              ),
            ),
          ),
        );
      
      case QREyeShape.star:
        return Icon(
          Icons.star_border,
          size: 24,
          color: color,
        );
      
      case QREyeShape.heart:
        return Icon(
          Icons.favorite_border,
          size: 24,
          color: color,
        );
      
      case QREyeShape.hexagon:
        return Icon(
          Icons.hexagon_outlined,
          size: 24,
          color: color,
        );
      
      case QREyeShape.triangle:
        return Icon(
          Icons.change_history,
          size: 24,
          color: color,
        );
      
      case QREyeShape.cross:
        return Icon(
          Icons.add,
          size: 24,
          color: color,
        );
      
      case QREyeShape.flower:
        return Icon(
          Icons.local_florist,
          size: 24,
          color: color,
        );
    }
  }
}
