import 'package:flutter/material.dart';
import '../models/qr_configuration.dart';
import '../models/qr_code_type.dart';
import '../models/qr_template.dart';
import '../services/qr_generator_service.dart';

class QRPreviewWidget extends StatelessWidget {
  final QRConfiguration? configuration;
  final double size;
  final bool showBorder;

  const QRPreviewWidget({
    super.key,
    this.configuration,
    this.size = 200,
    this.showBorder = true,
  });

  @override
  Widget build(BuildContext context) {
    if (configuration == null) {
      return _buildPlaceholder(context);
    }

    return Container(
      width: size,
      height: size,
      decoration: showBorder ? BoxDecoration(
        border: Border.all(
          color: Theme.of(context).colorScheme.outline,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ) : null,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(showBorder ? 11 : 0),
        child: _buildQRWithTemplate(context),
      ),
    );
  }

  Widget _buildQRWithTemplate(BuildContext context) {
    final qrService = QRGeneratorService();

    if (configuration!.template != null) {
      // Render QR with template
      return _buildTemplatedQR(context, qrService);
    } else {
      // Render simple QR
      return Container(
        color: configuration!.style.backgroundColor,
        child: Center(
          child: qrService.generateQRWidget(
            configuration!,
            size: size * 0.8, // Leave some padding
          ),
        ),
      );
    }
  }

  Widget _buildTemplatedQR(BuildContext context, QRGeneratorService qrService) {
    final template = configuration!.template!;

    return Stack(
      children: [
        // Template background
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            gradient: _getTemplateGradient(template),
          ),
        ),

        // QR Code positioned according to template
        Positioned(
          left: (template.qrPosition.x / 300) * size, // Scale position
          top: (template.qrPosition.y / 300) * size,
          child: Container(
            width: (template.qrPosition.width / 300) * size,
            height: (template.qrPosition.height / 300) * size,
            child: qrService.generateQRWidget(
              configuration!,
              size: (template.qrPosition.width / 300) * size,
            ),
          ),
        ),

        // Text overlays
        ...template.textOverlays.map((textOverlay) => Positioned(
          left: (textOverlay.x / 300) * size,
          top: (textOverlay.y / 300) * size,
          child: Text(
            textOverlay.text,
            style: TextStyle(
              fontSize: (textOverlay.fontSize / 300) * size * 0.1,
              color: textOverlay.color,
              fontWeight: textOverlay.fontWeight,
              fontFamily: textOverlay.fontFamily,
            ),
          ),
        )),

        // Template name overlay
        Positioned(
          bottom: 8,
          left: 8,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              template.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  LinearGradient _getTemplateGradient(QRTemplate template) {
    switch (template.category) {
      case TemplateCategory.hot:
        return const LinearGradient(
          colors: [Colors.orange, Colors.red],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case TemplateCategory.social:
        return const LinearGradient(
          colors: [Colors.purple, Colors.blue],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case TemplateCategory.love:
        return const LinearGradient(
          colors: [Colors.pink, Colors.red],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case TemplateCategory.business:
        return const LinearGradient(
          colors: [Colors.blueGrey, Colors.grey],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case TemplateCategory.wifi:
        return const LinearGradient(
          colors: [Colors.green, Colors.teal],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case TemplateCategory.all:
        return const LinearGradient(
          colors: [Colors.blue, Colors.indigo],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  Widget _buildPlaceholder(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border.all(
          color: Theme.of(context).colorScheme.outline,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.qr_code_2_outlined,
            size: size * 0.3,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'QR Preview',
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Enter data to see preview',
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class QRPreviewCard extends StatelessWidget {
  final QRConfiguration configuration;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool showActions;

  const QRPreviewCard({
    super.key,
    required this.configuration,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // QR Preview
              Center(
                child: QRPreviewWidget(
                  configuration: configuration,
                  size: 120,
                ),
              ),
              
              const SizedBox(height: 12),
              
              // QR Info
              Text(
                configuration.qrData.type.displayName,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 4),
              
              Text(
                _getPreviewText(),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 8),
              
              Text(
                _formatDate(configuration.createdAt),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                ),
              ),
              
              if (showActions) ...[
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (onEdit != null)
                      IconButton(
                        onPressed: onEdit,
                        icon: const Icon(Icons.edit),
                        tooltip: 'Edit',
                      ),
                    IconButton(
                      onPressed: () {
                        // TODO: Share functionality
                      },
                      icon: const Icon(Icons.share),
                      tooltip: 'Share',
                    ),
                    if (onDelete != null)
                      IconButton(
                        onPressed: onDelete,
                        icon: const Icon(Icons.delete),
                        tooltip: 'Delete',
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  String _getPreviewText() {
    final data = configuration.qrData.data;
    switch (configuration.qrData.type) {
      case QRCodeType.text:
        return data['text'] ?? '';
      case QRCodeType.url:
        return data['url'] ?? '';
      case QRCodeType.email:
        return data['email'] ?? '';
      case QRCodeType.phone:
        return data['phone'] ?? '';
      case QRCodeType.sms:
        return '${data['phone'] ?? ''} - ${data['message'] ?? ''}';
      case QRCodeType.wifi:
        return data['ssid'] ?? '';
      case QRCodeType.contact:
        return '${data['firstName'] ?? ''} ${data['lastName'] ?? ''}'.trim();
      case QRCodeType.whatsapp:
        return data['phone'] ?? '';
      case QRCodeType.telegram:
        return '@${data['username'] ?? ''}';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
