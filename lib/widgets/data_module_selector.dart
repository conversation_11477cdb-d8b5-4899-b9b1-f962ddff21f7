import 'package:flutter/material.dart';
import '../models/qr_style.dart';

class DataModuleSelector extends StatelessWidget {
  final QRDataModuleShape selectedShape;
  final ValueChanged<QRDataModuleShape> onShapeChanged;

  const DataModuleSelector({
    super.key,
    required this.selectedShape,
    required this.onShapeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1.2,
      ),
      itemCount: QRDataModuleShape.values.length,
      itemBuilder: (context, index) {
        final shape = QRDataModuleShape.values[index];
        final isSelected = shape == selectedShape;
        
        return GestureDetector(
          onTap: () => onShapeChanged(shape),
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected 
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.outline,
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
              color: isSelected 
                  ? Theme.of(context).colorScheme.primaryContainer
                  : Theme.of(context).colorScheme.surface,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildDataModulePattern(shape, context),
                const SizedBox(height: 4),
                Text(
                  shape.displayName,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected 
                        ? Theme.of(context).colorScheme.onPrimaryContainer
                        : Theme.of(context).colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDataModulePattern(QRDataModuleShape shape, BuildContext context) {
    final color = selectedShape == shape 
        ? Theme.of(context).colorScheme.onPrimaryContainer
        : Theme.of(context).colorScheme.onSurface;
    
    return SizedBox(
      width: 32,
      height: 32,
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 1,
          mainAxisSpacing: 1,
        ),
        itemCount: 16,
        itemBuilder: (context, index) {
          // Create a pattern where some modules are filled
          final shouldFill = (index % 3 == 0) || (index % 5 == 0);
          if (!shouldFill) return const SizedBox.shrink();
          
          return _buildSingleModule(shape, color);
        },
      ),
    );
  }

  Widget _buildSingleModule(QRDataModuleShape shape, Color color) {
    switch (shape) {
      case QRDataModuleShape.square:
        return Container(
          decoration: BoxDecoration(
            color: color,
          ),
        );
      
      case QRDataModuleShape.circle:
        return Container(
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        );
      
      case QRDataModuleShape.roundedSquare:
        return Container(
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(1),
          ),
        );
      
      case QRDataModuleShape.diamond:
        return Transform.rotate(
          angle: 0.785398, // 45 degrees
          child: Container(
            decoration: BoxDecoration(
              color: color,
            ),
          ),
        );
      
      case QRDataModuleShape.dot:
        return Center(
          child: Container(
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
        );
    }
  }
}
