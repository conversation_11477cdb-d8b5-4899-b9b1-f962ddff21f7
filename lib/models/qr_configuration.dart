import 'qr_code_type.dart';
import 'qr_style.dart';
import 'qr_template.dart';

class QRConfiguration {
  final QRCodeData qrData;
  final QRStyle style;
  final QRTemplate? template;
  final DateTime createdAt;
  final String? customName;

  const QRConfiguration({
    required this.qrData,
    required this.style,
    this.template,
    required this.createdAt,
    this.customName,
  });

  QRConfiguration copyWith({
    QRCodeData? qrData,
    QRStyle? style,
    QRTemplate? template,
    DateTime? createdAt,
    String? customName,
  }) {
    return QRConfiguration(
      qrData: qrData ?? this.qrData,
      style: style ?? this.style,
      template: template ?? this.template,
      createdAt: createdAt ?? this.createdAt,
      customName: customName ?? this.customName,
    );
  }

  String get fileName {
    final timestamp = createdAt.millisecondsSinceEpoch;
    final type = qrData.type.name;
    final name = customName ?? 'QR_code_${type}_$timestamp';
    return '$name.png';
  }

  Map<String, dynamic> toJson() {
    return {
      'qrData': qrData.toJson(),
      'style': style.toJson(),
      'template': template?.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'customName': customName,
    };
  }

  factory QRConfiguration.fromJson(Map<String, dynamic> json) {
    return QRConfiguration(
      qrData: QRCodeData.fromJson(json['qrData']),
      style: QRStyle.fromJson(json['style']),
      template: json['template'] != null ? QRTemplate.fromJson(json['template']) : null,
      createdAt: DateTime.parse(json['createdAt']),
      customName: json['customName'],
    );
  }
}

// Default configurations for quick start
class DefaultConfigurations {
  static const QRStyle defaultStyle = QRStyle();

  static QRConfiguration createDefault(QRCodeType type) {
    return QRConfiguration(
      qrData: QRCodeData(type: type, data: {}),
      style: defaultStyle,
      createdAt: DateTime.now(),
    );
  }

  static QRConfiguration createWithTemplate(QRCodeType type, QRTemplate template) {
    return QRConfiguration(
      qrData: QRCodeData(type: type, data: {}),
      style: defaultStyle,
      template: template,
      createdAt: DateTime.now(),
    );
  }
}
