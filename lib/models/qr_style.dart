import 'package:flutter/material.dart';

enum QREyeShape {
  square,
  circle,
  roundedSquare,
  diamond,
  star,
  heart,
  hexagon,
  triangle,
  cross,
  flower,
}

enum QRDataModuleShape {
  square,
  circle,
  roundedSquare,
  diamond,
  dot,
}

extension QREyeShapeExtension on QREyeShape {
  String get displayName {
    switch (this) {
      case QREyeShape.square:
        return 'Square';
      case QREyeShape.circle:
        return 'Circle';
      case QREyeShape.roundedSquare:
        return 'Rounded Square';
      case QREyeShape.diamond:
        return 'Diamond';
      case QREyeShape.star:
        return 'Star';
      case QREyeShape.heart:
        return 'Heart';
      case QREyeShape.hexagon:
        return 'Hexagon';
      case QREyeShape.triangle:
        return 'Triangle';
      case QREyeShape.cross:
        return 'Cross';
      case QREyeShape.flower:
        return 'Flower';
    }
  }
}

extension QRDataModuleShapeExtension on QRDataModuleShape {
  String get displayName {
    switch (this) {
      case QRDataModuleShape.square:
        return 'Square';
      case QRDataModuleShape.circle:
        return 'Circle';
      case QRDataModuleShape.roundedSquare:
        return 'Rounded Square';
      case QRDataModuleShape.diamond:
        return 'Diamond';
      case QRDataModuleShape.dot:
        return 'Dot';
    }
  }
}

class QRStyle {
  final QREyeShape eyeShape;
  final QRDataModuleShape dataModuleShape;
  final Color foregroundColor;
  final Color backgroundColor;
  final Color eyeColor;
  final double cornerRadius;
  final bool hasLogo;
  final String? logoPath;

  const QRStyle({
    this.eyeShape = QREyeShape.square,
    this.dataModuleShape = QRDataModuleShape.square,
    this.foregroundColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.eyeColor = Colors.black,
    this.cornerRadius = 0.0,
    this.hasLogo = false,
    this.logoPath,
  });

  QRStyle copyWith({
    QREyeShape? eyeShape,
    QRDataModuleShape? dataModuleShape,
    Color? foregroundColor,
    Color? backgroundColor,
    Color? eyeColor,
    double? cornerRadius,
    bool? hasLogo,
    String? logoPath,
  }) {
    return QRStyle(
      eyeShape: eyeShape ?? this.eyeShape,
      dataModuleShape: dataModuleShape ?? this.dataModuleShape,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      eyeColor: eyeColor ?? this.eyeColor,
      cornerRadius: cornerRadius ?? this.cornerRadius,
      hasLogo: hasLogo ?? this.hasLogo,
      logoPath: logoPath ?? this.logoPath,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'eyeShape': eyeShape.name,
      'dataModuleShape': dataModuleShape.name,
      'foregroundColor': foregroundColor.value,
      'backgroundColor': backgroundColor.value,
      'eyeColor': eyeColor.value,
      'cornerRadius': cornerRadius,
      'hasLogo': hasLogo,
      'logoPath': logoPath,
    };
  }

  factory QRStyle.fromJson(Map<String, dynamic> json) {
    return QRStyle(
      eyeShape: QREyeShape.values.firstWhere((e) => e.name == json['eyeShape']),
      dataModuleShape: QRDataModuleShape.values.firstWhere((e) => e.name == json['dataModuleShape']),
      foregroundColor: Color(json['foregroundColor']),
      backgroundColor: Color(json['backgroundColor']),
      eyeColor: Color(json['eyeColor']),
      cornerRadius: json['cornerRadius']?.toDouble() ?? 0.0,
      hasLogo: json['hasLogo'] ?? false,
      logoPath: json['logoPath'],
    );
  }
}
