enum QRCodeType {
  text,
  url,
  email,
  phone,
  sms,
  wifi,
  contact,
  whatsapp,
  telegram,
}

extension QRCodeTypeExtension on QRCodeType {
  String get displayName {
    switch (this) {
      case QRCodeType.text:
        return 'Text';
      case QRCodeType.url:
        return 'URL';
      case QRCodeType.email:
        return 'Email';
      case QRCodeType.phone:
        return 'Phone';
      case QRCodeType.sms:
        return 'SMS';
      case QRCodeType.wifi:
        return 'Wi-Fi';
      case QRCodeType.contact:
        return 'Contact';
      case QRCodeType.whatsapp:
        return 'WhatsApp';
      case QRCodeType.telegram:
        return 'Telegram';
    }
  }

  String get icon {
    switch (this) {
      case QRCodeType.text:
        return '📝';
      case QRCodeType.url:
        return '🌐';
      case QRCodeType.email:
        return '📧';
      case QRCodeType.phone:
        return '📞';
      case QRCodeType.sms:
        return '💬';
      case QRCodeType.wifi:
        return '📶';
      case QRCodeType.contact:
        return '👤';
      case QRCodeType.whatsapp:
        return '💚';
      case QRCodeType.telegram:
        return '✈️';
    }
  }
}

class QRCodeData {
  final QRCodeType type;
  final Map<String, dynamic> data;

  QRCodeData({
    required this.type,
    required this.data,
  });

  String generateQRString() {
    switch (type) {
      case QRCodeType.text:
        return data['text'] ?? '';
      case QRCodeType.url:
        return data['url'] ?? '';
      case QRCodeType.email:
        return 'mailto:${data['email']}?subject=${data['subject'] ?? ''}&body=${data['body'] ?? ''}';
      case QRCodeType.phone:
        return 'tel:${data['phone']}';
      case QRCodeType.sms:
        return 'sms:${data['phone']}?body=${data['message'] ?? ''}';
      case QRCodeType.wifi:
        return 'WIFI:T:${data['security']};S:${data['ssid']};P:${data['password']};H:${data['hidden'] ?? false};';
      case QRCodeType.contact:
        return _generateVCard();
      case QRCodeType.whatsapp:
        return 'https://wa.me/${data['phone']}?text=${Uri.encodeComponent(data['message'] ?? '')}';
      case QRCodeType.telegram:
        return 'https://t.me/${data['username']}';
    }
  }

  String _generateVCard() {
    final firstName = data['firstName'] ?? '';
    final lastName = data['lastName'] ?? '';
    final phone = data['phone'] ?? '';
    final email = data['email'] ?? '';
    final organization = data['organization'] ?? '';
    final url = data['url'] ?? '';

    return '''BEGIN:VCARD
VERSION:3.0
FN:$firstName $lastName
N:$lastName;$firstName;;;
TEL:$phone
EMAIL:$email
ORG:$organization
URL:$url
END:VCARD''';
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'data': data,
    };
  }

  factory QRCodeData.fromJson(Map<String, dynamic> json) {
    return QRCodeData(
      type: QRCodeType.values.firstWhere((e) => e.name == json['type']),
      data: json['data'],
    );
  }
}
