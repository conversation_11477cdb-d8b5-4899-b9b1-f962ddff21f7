import 'package:flutter/material.dart';

enum TemplateCategory {
  hot,
  social,
  love,
  business,
  wifi,
  all,
}

extension TemplateCategoryExtension on TemplateCategory {
  String get displayName {
    switch (this) {
      case TemplateCategory.hot:
        return 'Hot';
      case TemplateCategory.social:
        return 'Social';
      case TemplateCategory.love:
        return 'Love';
      case TemplateCategory.business:
        return 'Business';
      case TemplateCategory.wifi:
        return 'Wi-Fi';
      case TemplateCategory.all:
        return 'All';
    }
  }

  String get icon {
    switch (this) {
      case TemplateCategory.hot:
        return '🔥';
      case TemplateCategory.social:
        return '📱';
      case TemplateCategory.love:
        return '💕';
      case TemplateCategory.business:
        return '💼';
      case TemplateCategory.wifi:
        return '📶';
      case TemplateCategory.all:
        return '🎨';
    }
  }
}

class QRTemplate {
  final String id;
  final String name;
  final String description;
  final TemplateCategory category;
  final String backgroundImagePath;
  final QRPosition qrPosition;
  final List<TextOverlay> textOverlays;
  final List<IconOverlay> iconOverlays;
  final String thumbnailPath;
  final bool isPremium;

  const QRTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.backgroundImagePath,
    required this.qrPosition,
    this.textOverlays = const [],
    this.iconOverlays = const [],
    required this.thumbnailPath,
    this.isPremium = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category.name,
      'backgroundImagePath': backgroundImagePath,
      'qrPosition': qrPosition.toJson(),
      'textOverlays': textOverlays.map((e) => e.toJson()).toList(),
      'iconOverlays': iconOverlays.map((e) => e.toJson()).toList(),
      'thumbnailPath': thumbnailPath,
      'isPremium': isPremium,
    };
  }

  factory QRTemplate.fromJson(Map<String, dynamic> json) {
    return QRTemplate(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      category: TemplateCategory.values.firstWhere((e) => e.name == json['category']),
      backgroundImagePath: json['backgroundImagePath'],
      qrPosition: QRPosition.fromJson(json['qrPosition']),
      textOverlays: (json['textOverlays'] as List?)
          ?.map((e) => TextOverlay.fromJson(e))
          .toList() ?? [],
      iconOverlays: (json['iconOverlays'] as List?)
          ?.map((e) => IconOverlay.fromJson(e))
          .toList() ?? [],
      thumbnailPath: json['thumbnailPath'],
      isPremium: json['isPremium'] ?? false,
    );
  }
}

class QRPosition {
  final double x;
  final double y;
  final double width;
  final double height;

  const QRPosition({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
    };
  }

  factory QRPosition.fromJson(Map<String, dynamic> json) {
    return QRPosition(
      x: json['x']?.toDouble() ?? 0.0,
      y: json['y']?.toDouble() ?? 0.0,
      width: json['width']?.toDouble() ?? 200.0,
      height: json['height']?.toDouble() ?? 200.0,
    );
  }
}

class TextOverlay {
  final String text;
  final double x;
  final double y;
  final double fontSize;
  final Color color;
  final String fontFamily;
  final FontWeight fontWeight;

  const TextOverlay({
    required this.text,
    required this.x,
    required this.y,
    this.fontSize = 16.0,
    this.color = Colors.black,
    this.fontFamily = 'Roboto',
    this.fontWeight = FontWeight.normal,
  });

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'x': x,
      'y': y,
      'fontSize': fontSize,
      'color': color.value,
      'fontFamily': fontFamily,
      'fontWeight': fontWeight.index,
    };
  }

  factory TextOverlay.fromJson(Map<String, dynamic> json) {
    return TextOverlay(
      text: json['text'],
      x: json['x']?.toDouble() ?? 0.0,
      y: json['y']?.toDouble() ?? 0.0,
      fontSize: json['fontSize']?.toDouble() ?? 16.0,
      color: Color(json['color'] ?? Colors.black.value),
      fontFamily: json['fontFamily'] ?? 'Roboto',
      fontWeight: FontWeight.values[json['fontWeight'] ?? FontWeight.normal.index],
    );
  }
}

class IconOverlay {
  final String iconPath;
  final double x;
  final double y;
  final double size;

  const IconOverlay({
    required this.iconPath,
    required this.x,
    required this.y,
    this.size = 24.0,
  });

  Map<String, dynamic> toJson() {
    return {
      'iconPath': iconPath,
      'x': x,
      'y': y,
      'size': size,
    };
  }

  factory IconOverlay.fromJson(Map<String, dynamic> json) {
    return IconOverlay(
      iconPath: json['iconPath'],
      x: json['x']?.toDouble() ?? 0.0,
      y: json['y']?.toDouble() ?? 0.0,
      size: json['size']?.toDouble() ?? 24.0,
    );
  }
}
