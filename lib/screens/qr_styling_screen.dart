import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../models/qr_configuration.dart';
import '../models/qr_style.dart';
import '../widgets/qr_preview_widget.dart';
import '../widgets/eye_shape_selector.dart';
import '../widgets/data_module_selector.dart';

class QRStylingScreen extends StatefulWidget {
  final QRConfiguration initialConfig;
  final ValueChanged<QRConfiguration> onConfigChanged;

  const QRStylingScreen({
    super.key,
    required this.initialConfig,
    required this.onConfigChanged,
  });

  @override
  State<QRStylingScreen> createState() => _QRStylingScreenState();
}

class _QRStylingScreenState extends State<QRStylingScreen> {
  late QRConfiguration _currentConfig;

  @override
  void initState() {
    super.initState();
    _currentConfig = widget.initialConfig;
  }

  void _updateStyle(QRStyle newStyle) {
    setState(() {
      _currentConfig = _currentConfig.copyWith(style: newStyle);
    });
    widget.onConfigChanged(_currentConfig);
  }

  void _showColorPicker({
    required String title,
    required Color currentColor,
    required ValueChanged<Color> onColorChanged,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: currentColor,
            onColorChanged: onColorChanged,
            pickerAreaHeightPercent: 0.8,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('QR Code Styling'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.check),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
      body: Row(
        children: [
          // Styling Controls (Left Side)
          Expanded(
            flex: 1,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Eye Shape Section
                  _buildSectionTitle('Eye Shape'),
                  const SizedBox(height: 12),
                  EyeShapeSelector(
                    selectedShape: _currentConfig.style.eyeShape,
                    onShapeChanged: (shape) {
                      _updateStyle(_currentConfig.style.copyWith(eyeShape: shape));
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Data Module Shape Section
                  _buildSectionTitle('Data Pattern'),
                  const SizedBox(height: 12),
                  DataModuleSelector(
                    selectedShape: _currentConfig.style.dataModuleShape,
                    onShapeChanged: (shape) {
                      _updateStyle(_currentConfig.style.copyWith(dataModuleShape: shape));
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Colors Section
                  _buildSectionTitle('Colors'),
                  const SizedBox(height: 12),
                  
                  // Foreground Color
                  _buildColorOption(
                    'Foreground Color',
                    _currentConfig.style.foregroundColor,
                    (color) => _updateStyle(_currentConfig.style.copyWith(foregroundColor: color)),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Background Color
                  _buildColorOption(
                    'Background Color',
                    _currentConfig.style.backgroundColor,
                    (color) => _updateStyle(_currentConfig.style.copyWith(backgroundColor: color)),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Eye Color
                  _buildColorOption(
                    'Eye Color',
                    _currentConfig.style.eyeColor,
                    (color) => _updateStyle(_currentConfig.style.copyWith(eyeColor: color)),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Corner Radius Section
                  _buildSectionTitle('Corner Radius'),
                  const SizedBox(height: 12),
                  Slider(
                    value: _currentConfig.style.cornerRadius,
                    min: 0,
                    max: 20,
                    divisions: 20,
                    label: _currentConfig.style.cornerRadius.round().toString(),
                    onChanged: (value) {
                      _updateStyle(_currentConfig.style.copyWith(cornerRadius: value));
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Logo Section
                  _buildSectionTitle('Logo'),
                  const SizedBox(height: 12),
                  SwitchListTile(
                    title: const Text('Add Logo'),
                    subtitle: const Text('Embed a logo in the center'),
                    value: _currentConfig.style.hasLogo,
                    onChanged: (value) {
                      _updateStyle(_currentConfig.style.copyWith(hasLogo: value));
                    },
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Preset Styles
                  _buildSectionTitle('Preset Styles'),
                  const SizedBox(height: 12),
                  _buildPresetStyles(),
                ],
              ),
            ),
          ),
          
          // Divider
          const VerticalDivider(width: 1),
          
          // Preview (Right Side)
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text(
                    'Preview',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: Center(
                      child: QRPreviewWidget(
                        configuration: _currentConfig,
                        size: 300,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildColorOption(String label, Color color, ValueChanged<Color> onChanged) {
    return ListTile(
      title: Text(label),
      trailing: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color,
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      onTap: () => _showColorPicker(
        title: label,
        currentColor: color,
        onColorChanged: onChanged,
      ),
    );
  }

  Widget _buildPresetStyles() {
    final presets = [
      {'name': 'Classic', 'style': const QRStyle()},
      {
        'name': 'Modern',
        'style': const QRStyle(
          eyeShape: QREyeShape.roundedSquare,
          dataModuleShape: QRDataModuleShape.circle,
          foregroundColor: Color(0xFF1976D2),
          cornerRadius: 8,
        )
      },
      {
        'name': 'Elegant',
        'style': const QRStyle(
          eyeShape: QREyeShape.circle,
          dataModuleShape: QRDataModuleShape.roundedSquare,
          foregroundColor: Color(0xFF7B1FA2),
          eyeColor: Color(0xFF4A148C),
          cornerRadius: 12,
        )
      },
      {
        'name': 'Bold',
        'style': const QRStyle(
          eyeShape: QREyeShape.diamond,
          dataModuleShape: QRDataModuleShape.diamond,
          foregroundColor: Color(0xFFD32F2F),
          eyeColor: Color(0xFF8B0000),
          cornerRadius: 4,
        )
      },
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: presets.map((preset) {
        return ElevatedButton(
          onPressed: () => _updateStyle(preset['style'] as QRStyle),
          child: Text(preset['name'] as String),
        );
      }).toList(),
    );
  }
}
