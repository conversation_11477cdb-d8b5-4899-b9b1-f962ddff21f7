import 'dart:io';
import 'package:flutter/material.dart';
import '../services/file_service.dart';
import '../models/qr_configuration.dart';
import '../widgets/qr_preview_widget.dart';

class SavedQRScreen extends StatefulWidget {
  const SavedQRScreen({super.key});

  @override
  State<SavedQRScreen> createState() => _SavedQRScreenState();
}

class _SavedQRScreenState extends State<SavedQRScreen> {
  final FileService _fileService = FileService();
  List<File> _savedFiles = [];
  List<QRConfiguration> _savedConfigs = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSavedQRCodes();
  }

  Future<void> _loadSavedQRCodes() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final files = await _fileService.getSavedQRCodes();
      final configs = await _fileService.loadConfigurations();

      setState(() {
        _savedFiles = files;
        _savedConfigs = configs;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading saved QR codes: $e')),
        );
      }
    }
  }

  Future<void> _deleteQRCode(int index) async {
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete QR Code'),
        content: const Text('Are you sure you want to delete this QR code?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (shouldDelete == true) {
      try {
        if (index < _savedFiles.length) {
          await _savedFiles[index].delete();
        }
        await _loadSavedQRCodes(); // Refresh the list
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('QR code deleted successfully')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting QR code: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Saved QR Codes'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSavedQRCodes,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _savedConfigs.isEmpty
              ? _buildEmptyState()
              : _buildQRCodesList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 100,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No Saved QR Codes',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Create and save QR codes to see them here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Navigate to QR generator
              DefaultTabController.of(context)?.animateTo(0);
            },
            icon: const Icon(Icons.add),
            label: const Text('Create QR Code'),
          ),
        ],
      ),
    );
  }

  Widget _buildQRCodesList() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.8,
      ),
      itemCount: _savedConfigs.length,
      itemBuilder: (context, index) {
        final config = _savedConfigs[index];
        return QRPreviewCard(
          configuration: config,
          onTap: () {
            // TODO: Navigate to QR generator with this config
          },
          onEdit: () {
            // TODO: Navigate to QR generator with this config for editing
          },
          onDelete: () => _deleteQRCode(index),
        );
      },
    );
  }
}
