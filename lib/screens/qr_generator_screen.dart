import 'package:flutter/material.dart';
import '../models/qr_code_type.dart';
import '../models/qr_configuration.dart';
import '../models/qr_style.dart';
import '../models/qr_template.dart';
import '../services/qr_generator_service.dart';
import '../services/file_service.dart';
import '../widgets/qr_type_selector.dart';
import '../widgets/qr_input_form.dart';
import '../widgets/qr_preview_widget.dart';
import 'qr_styling_screen.dart';
import 'template_gallery_screen.dart';

class QRGeneratorScreen extends StatefulWidget {
  const QRGeneratorScreen({super.key});

  @override
  State<QRGeneratorScreen> createState() => _QRGeneratorScreenState();
}

class _QRGeneratorScreenState extends State<QRGeneratorScreen> {
  QRCodeType _selectedType = QRCodeType.text;
  Map<String, dynamic> _inputData = {};
  QRConfiguration? _currentConfig;
  final QRGeneratorService _qrService = QRGeneratorService();
  final FileService _fileService = FileService();
  String? _validationError;
  bool _isSaving = false;
  bool _isSharing = false;

  @override
  void initState() {
    super.initState();
    _updateConfiguration();
  }

  void _onTypeChanged(QRCodeType type) {
    setState(() {
      _selectedType = type;
      _inputData.clear();
      _validationError = null;
      _updateConfiguration();
    });
  }

  void _onInputChanged(Map<String, dynamic> data) {
    setState(() {
      _inputData = data;
      _validateAndUpdateConfig();
    });
  }

  void _validateAndUpdateConfig() {
    final validation = _qrService.validateQRData(_selectedType, _inputData);
    _validationError = validation.isValid ? null : validation.error;

    if (validation.isValid) {
      _updateConfiguration();
    }
  }

  void _updateConfiguration() {
    _currentConfig = QRConfiguration(
      qrData: QRCodeData(type: _selectedType, data: _inputData),
      style: _currentConfig?.style ?? const QRStyle(),
      template: _currentConfig?.template,
      createdAt: DateTime.now(),
    );
  }

  void _applyTemplate(QRTemplate template) {
    setState(() {
      _currentConfig = QRConfiguration(
        qrData: QRCodeData(type: _selectedType, data: _inputData),
        style: const QRStyle(), // Use default style, template will override visuals
        template: template,
        createdAt: DateTime.now(),
      );
    });

    // Show confirmation
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Applied template: ${template.name}'),
        action: SnackBarAction(
          label: 'Undo',
          onPressed: () {
            setState(() {
              _currentConfig = _currentConfig?.copyWith(template: null);
            });
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('QR Generator'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.photo_library),
            onPressed: () async {
              final template = await Navigator.of(context).push<QRTemplate>(
                MaterialPageRoute(
                  builder: (context) => const TemplateGalleryScreen(),
                ),
              );
              if (template != null) {
                _applyTemplate(template);
              }
            },
            tooltip: 'Templates',
          ),
          IconButton(
            icon: const Icon(Icons.palette),
            onPressed: _currentConfig != null ? () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => QRStylingScreen(
                    initialConfig: _currentConfig!,
                    onConfigChanged: (config) {
                      setState(() {
                        _currentConfig = config;
                      });
                    },
                  ),
                ),
              );
            } : null,
            tooltip: 'Styling',
          ),
        ],
      ),
      body: Column(
        children: [
          // QR Type Selector
          Container(
            padding: const EdgeInsets.all(16),
            child: QRTypeSelector(
              selectedType: _selectedType,
              onTypeChanged: _onTypeChanged,
            ),
          ),

          Expanded(
            child: Row(
              children: [
                // Input Form (Left Side)
                Expanded(
                  flex: 1,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Enter ${_selectedType.displayName} Details',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 16),
                        Expanded(
                          child: QRInputForm(
                            type: _selectedType,
                            initialData: _inputData,
                            onDataChanged: _onInputChanged,
                            validationError: _validationError,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Divider
                const VerticalDivider(width: 1),

                // QR Preview (Right Side)
                Expanded(
                  flex: 1,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          'QR Code Preview',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 16),
                        Expanded(
                          child: Center(
                            child: QRPreviewWidget(
                              configuration: _currentConfig,
                              size: 300,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            ElevatedButton.icon(
                              onPressed: _currentConfig != null && _validationError == null && !_isSaving
                                  ? _saveQRCode
                                  : null,
                              icon: _isSaving
                                  ? const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(strokeWidth: 2),
                                    )
                                  : const Icon(Icons.save),
                              label: Text(_isSaving ? 'Saving...' : 'Save'),
                            ),
                            ElevatedButton.icon(
                              onPressed: _currentConfig != null && _validationError == null && !_isSharing
                                  ? _shareQRCode
                                  : null,
                              icon: _isSharing
                                  ? const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(strokeWidth: 2),
                                    )
                                  : const Icon(Icons.share),
                              label: Text(_isSharing ? 'Sharing...' : 'Share'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _saveQRCode() async {
    if (_currentConfig == null) return;

    setState(() {
      _isSaving = true;
    });

    try {
      // Generate QR code image
      final imageBytes = await _qrService.generateQRImage(_currentConfig!);

      if (imageBytes != null) {
        // Save to gallery/documents
        final result = await _fileService.saveToGallery(imageBytes, _currentConfig!);

        if (result.success) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('QR code saved successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Failed to save: ${result.error}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to generate QR code image'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving QR code: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _shareQRCode() async {
    if (_currentConfig == null) return;

    setState(() {
      _isSharing = true;
    });

    try {
      // Generate QR code image
      final imageBytes = await _qrService.generateQRImage(_currentConfig!);

      if (imageBytes != null) {
        // Share the image
        final result = await _fileService.shareImage(imageBytes, _currentConfig!);

        if (!result.success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to share: ${result.error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to generate QR code image'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing QR code: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSharing = false;
        });
      }
    }
  }
}
