📘 Project Requirements: Stylish QR Code Generator App

Project Title: QRStyler
Platform: Flutter (Cross-platform – Android & iOS)
Version: MVP v1.0
Monetization: None (for now – no In-App Purchases)
Target Users: Small businesses, influencers, freelancers, marketers, students

⸻

1. 🎯 Goal of the Project

To develop a mobile application that allows users to:
	•	Create custom QR codes for different data types (URL, contact, WiFi, etc.)
	•	Style QR codes with customizable eyes, colors, and shapes
	•	Apply ready-made design templates with artistic backgrounds and layouts
	•	Export and share QR codes

⸻

2. 👨‍💻 User Roles
Role
Access/Features
Visitor
Full access to all features (no login required)


3. 🧩 Core Features

A. QR Code Generator
	•	Input types:
	•	Text / URL
	•	Email address
	•	Phone number
	•	SMS
	•	Wi-Fi (SSID + Password)
	•	Contact (vCard format)
	•	WhatsApp / Telegram message
	•	Validate input before generating QR code
	•	Preview live QR as user types

⸻

B. QR Styling
	•	Eye shape selector (10+ styles)
	•	Dot/data pattern style selector (square, circle, rounded, etc.)
	•	Foreground color
	•	Background color
	•	Eye color customization
	•	Corner radius (optional)
	•	Add central logo (optional future)

⸻

C. QR Code Templates
	•	25+ pre-designed QR templates:
	•	Categories: Hot, Social, Love, Business, Wi-Fi
	•	Each template includes:
	•	Background image (PNG/JPG)
	•	QR code position (x, y, width, height)
	•	Optional text or icon overlays
	•	Templates loaded from local JSON

⸻

D. Template Management
	•	View template grid with preview thumbnails
	•	Filter tabs (Hot, Social, Business, etc.)
	•	Search template by keyword
	•	Apply template and preview QR

⸻

E. Save & Share
	•	Save final QR as PNG/JPG to gallery
	•	Share QR via:
	•	WhatsApp
	•	Telegram
	•	Email
	•	Any system share app
	•	Auto-generate image title: QR_code_<type>_<timestamp>.png

⸻
